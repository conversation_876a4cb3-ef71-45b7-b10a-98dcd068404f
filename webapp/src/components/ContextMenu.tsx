import React, { useState, useEffect, useRef, ReactNode } from 'react';

interface ContextMenuProps {
  children: ReactNode;
  items: {
    label: string;
    icon?: ReactNode;
    onClick: () => void;
    disabled?: boolean;
    divider?: boolean;
    danger?: boolean;
  }[];
}

interface Position {
  x: number;
  y: number;
}

const ContextMenu: React.FC<ContextMenuProps> = ({ children, items }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleContextMenu = (e: React.MouseEvent) => {
    // Check if the right-click occurred on a file/folder item
    const target = e.target as HTMLElement;
    const fileItem = target.closest('.file-item');
    const folderExplorerItem = target.closest('.folder-explorer-item');
    const folderExplorerName = target.closest('.folder-explorer-name');

    // Check if we're in the toolbar or breadcrumb area
    const toolbar = target.closest('.file-manager-toolbar');
    const breadcrumb = target.closest('.file-manager-breadcrumb');

    // Only allow right-click in the files/folders area
    if (toolbar || breadcrumb) {
      return;
    }

    // If we're right-clicking on a file/folder item, check if this is the innermost ContextMenu
    if (fileItem) {
      // Find the closest ContextMenu container to the file item
      const closestContextMenu = fileItem.closest('.relative');

      // If this ContextMenu is not the closest one to the file item, don't handle the event
      if (closestContextMenu !== containerRef.current) {
        return;
      }

      // This is the correct ContextMenu for this file item
      e.preventDefault();
      e.stopPropagation(); // Prevent bubbling to parent ContextMenus

      // Calculate position relative to the viewport
      const x = e.clientX;
      const y = e.clientY;

      setPosition({ x, y });
      setIsVisible(true);
      return;
    }

    // If we're right-clicking on a folder in the folder explorer,
    // show the context menu for that folder
    if (folderExplorerItem && folderExplorerName) {
      e.preventDefault();

      // Calculate position relative to the viewport
      const x = e.clientX;
      const y = e.clientY;

      setPosition({ x, y });
      setIsVisible(true);
      return;
    }

    e.preventDefault();

    // Calculate position relative to the viewport
    const x = e.clientX;
    const y = e.clientY;

    setPosition({ x, y });
    setIsVisible(true);
  };

  const handleClick = (callback: () => void) => {
    callback();
    setIsVisible(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        setIsVisible(false);
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleOutsideClick);
    }

    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isVisible]);

  // Close menu when pressing Escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsVisible(false);
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isVisible]);

  // Adjust menu position if it would render outside viewport
  useEffect(() => {
    if (isVisible && menuRef.current) {
      const menuRect = menuRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let adjustedX = position.x;
      let adjustedY = position.y;

      if (position.x + menuRect.width > viewportWidth) {
        adjustedX = viewportWidth - menuRect.width - 10;
      }

      if (position.y + menuRect.height > viewportHeight) {
        adjustedY = viewportHeight - menuRect.height - 10;
      }

      if (adjustedX !== position.x || adjustedY !== position.y) {
        setPosition({ x: adjustedX, y: adjustedY });
      }
    }
  }, [isVisible, position]);

  // Handle double click event
  const handleDoubleClick = (e: React.MouseEvent) => {
    // Close the context menu if it's open
    if (isVisible) {
      setIsVisible(false);
      // Stop propagation only if the menu is visible
      e.stopPropagation();
    }
    // Otherwise, let the event propagate to handle folder opening
  };

  return (
    <div
      ref={containerRef}
      onContextMenu={handleContextMenu}
      onDoubleClick={handleDoubleClick}
      className="relative"
    >
      {children}

      {isVisible && (
        <div
          ref={menuRef}
          className="fixed z-50 bg-white rounded-md shadow-lg border border-gray-200 py-1 min-w-[160px]"
          style={{
            left: `${position.x}px`,
            top: `${position.y}px`
          }}
        >
          {items.map((item, index) => (
            <div key={index}>
              {item.divider && index > 0 && (
                <div className="border-t border-gray-200 my-1"></div>
              )}
              <button
                onClick={() => handleClick(item.onClick)}
                disabled={item.disabled}
                className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                  item.disabled
                    ? 'text-gray-400 cursor-not-allowed'
                    : item.danger
                      ? 'text-red-600 hover:bg-red-50'
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {item.icon && <span className="mr-2">{item.icon}</span>}
                {item.label}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ContextMenu;
